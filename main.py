import logging
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
import os
from dotenv import load_dotenv

from database import get_db, engine
from models import Base
from auth import get_current_admin, create_access_token, verify_password
from routers import auth, dashboard, leads, products, team, sales, goals, followups, preorders, export, debug

load_dotenv()

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Criar tabelas
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Multi-Tenancy Hub API",
    description="API para dashboard multi-tenant",
    version="1.0.0"
)

# CORS mais restritivo
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "https://high-insights-web-681379300571.us-east1.run.app"],  # Especifique domínios
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],
)

# Adicionar middleware para trailing slash (opcional)
from fastapi.middleware.trustedhost import TrustedHostMiddleware

app.add_middleware(
    TrustedHostMiddleware, 
    allowed_hosts=["localhost", "127.0.0.1", "igh-insights-web-681379300571.us-east1.run.app"]
)

# Incluir routers
app.include_router(auth.router, prefix="/auth", tags=["auth"])
app.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
app.include_router(leads.router, prefix="/leads", tags=["leads"])
app.include_router(products.router, prefix="/products", tags=["products"])
app.include_router(team.router, prefix="/team", tags=["team"])
app.include_router(sales.router, prefix="/sales", tags=["sales"])
app.include_router(goals.router, prefix="/goals", tags=["goals"])
app.include_router(followups.router, prefix="/followups", tags=["followups"])
app.include_router(preorders.router, prefix="/preorders", tags=["preorders"])
app.include_router(export.router, prefix="/export", tags=["export"])

# Router de debug (apenas quando DEBUG=True)
import os
if os.getenv("DEBUG") == "True":
    app.include_router(debug.router, prefix="/debug", tags=["debug"])

@app.middleware("http")
async def log_requests(request, call_next):
    logger.info(f"Request: {request.method} {request.url}")
    response = await call_next(request)
    logger.info(f"Response: {response.status_code}")
    return response

@app.get("/")
async def root():
    return {"message": "Multi-Tenancy Hub API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
