from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from database import get_db
from models import Admin, Followup
from schemas import FollowupCreate, FollowupUpdate, FollowupResponse
from auth import get_current_admin

router = APIRouter()

@router.get("/", response_model=List[FollowupResponse])
async def get_followups(
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    followups = db.query(Followup).filter(Followup.company_id == current_admin.company_id).all()
    return followups

@router.post("/", response_model=FollowupResponse)
async def create_followup(
    followup_data: FollowupCreate,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    followup = Followup(
        company_id=current_admin.company_id,
        **followup_data.dict()
    )
    db.add(followup)
    db.commit()
    db.refresh(followup)
    return followup

@router.get("/{followup_id}", response_model=FollowupResponse)
async def get_followup(
    followup_id: int,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    followup = db.query(Followup).filter(
        Followup.id == followup_id,
        Followup.company_id == current_admin.company_id
    ).first()
    
    if not followup:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Follow-up não encontrado"
        )
    
    return followup

@router.put("/{followup_id}", response_model=FollowupResponse)
async def update_followup(
    followup_id: int,
    followup_data: FollowupUpdate,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    followup = db.query(Followup).filter(
        Followup.id == followup_id,
        Followup.company_id == current_admin.company_id
    ).first()
    
    if not followup:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Follow-up não encontrado"
        )
    
    update_data = followup_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(followup, field, value)
    
    db.commit()
    db.refresh(followup)
    return followup

@router.delete("/{followup_id}")
async def delete_followup(
    followup_id: int,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    followup = db.query(Followup).filter(
        Followup.id == followup_id,
        Followup.company_id == current_admin.company_id
    ).first()
    
    if not followup:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Follow-up não encontrado"
        )
    
    db.delete(followup)
    db.commit()
    return {"message": "Follow-up deletado com sucesso"}