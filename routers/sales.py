from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from database import get_db
from models import Admin, Sale
from schemas import SaleCreate, SaleUpdate, SaleResponse
from auth import get_current_admin

router = APIRouter()

@router.get("/", response_model=List[SaleResponse])
async def get_sales(
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    sales = db.query(Sale).filter(Sale.company_id == current_admin.company_id).all()
    return sales

@router.post("/", response_model=SaleResponse)
async def create_sale(
    sale_data: SaleCreate,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    sale = Sale(
        company_id=current_admin.company_id,
        **sale_data.dict()
    )
    db.add(sale)
    db.commit()
    db.refresh(sale)
    return sale

@router.get("/{sale_id}", response_model=SaleResponse)
async def get_sale(
    sale_id: int,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    sale = db.query(Sale).filter(
        Sale.id == sale_id,
        Sale.company_id == current_admin.company_id
    ).first()
    
    if not sale:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Venda não encontrada"
        )
    
    return sale

@router.put("/{sale_id}", response_model=SaleResponse)
async def update_sale(
    sale_id: int,
    sale_data: SaleUpdate,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    sale = db.query(Sale).filter(
        Sale.id == sale_id,
        Sale.company_id == current_admin.company_id
    ).first()
    
    if not sale:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Venda não encontrada"
        )
    
    update_data = sale_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(sale, field, value)
    
    db.commit()
    db.refresh(sale)
    return sale

@router.delete("/{sale_id}")
async def delete_sale(
    sale_id: int,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    sale = db.query(Sale).filter(
        Sale.id == sale_id,
        Sale.company_id == current_admin.company_id
    ).first()
    
    if not sale:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Venda não encontrada"
        )
    
    db.delete(sale)
    db.commit()
    return {"message": "Venda deletada com sucesso"}