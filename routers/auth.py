import sys
import os

# Ajustar o Python path para importações locais (necessário para debug)
if __name__ == "__main__":
    backend_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if backend_path not in sys.path:
        sys.path.insert(0, backend_path)

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from database import get_db
from models import Admin, Company
from schemas import LoginRequest, LoginResponse
from auth import authenticate_admin, create_access_token
from datetime import timedelta
from debug_utils import debug_query_result, debug_table_info, debug_session_queries, debug_model_data
import os

router = APIRouter()

@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest, db: Session = Depends(get_db)):
    
    # 🐛 DEBUG: Informações da sessão e tabelas
    if os.getenv("DEBUG") == "True":
        debug_session_queries(db)
        debug_table_info(db, "companies")
        debug_table_info(db, "admins")
    
    # 🔍 Autenticar admin
    admin = authenticate_admin(db, login_data.username, login_data.password)
    
    # 🐛 DEBUG: Resultado da autenticação
    if os.getenv("DEBUG") == "True":
        debug_model_data(admin, "Admin autenticado")
    
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Credenciais inválidas",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 🔍 Buscar dados da empresa
    company_query = db.query(Company).filter(Company.id == admin.company_id)
    
    # 🐛 DEBUG: Query da empresa
    if os.getenv("DEBUG") == "True":
        debug_query_result(str(company_query), company_query.all(), "Busca da empresa")
    
    company = company_query.first()
    
    # 🐛 DEBUG: Empresa encontrada
    if os.getenv("DEBUG") == "True":
        debug_model_data(company, "Empresa")
    
    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Empresa não encontrada"
        )
    
    # Criar token JWT
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": str(admin.id), "company_id": admin.company_id},
        expires_delta=access_token_expires
    )
    
    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        company={
            "id": company.id,
            "name": company.name
        }
    )

# Bloco para debug e teste
if __name__ == "__main__":
    import sys
    import os
    
    # Adicionar o diretório backend ao Python path para debug
    backend_path = os.path.dirname(os.path.abspath(__file__))
    parent_path = os.path.dirname(backend_path)
    if parent_path not in sys.path:
        sys.path.insert(0, parent_path)
    
    print("🐛 Debug mode - Auth router")
    print(f"✅ Todas as importações funcionando!")
    print(f"📁 Diretório backend: {parent_path}")
    print(f"🚀 Para testar a API completa, execute: python main.py")