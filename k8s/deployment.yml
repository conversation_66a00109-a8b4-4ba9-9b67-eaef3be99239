apiVersion: apps/v1
kind: Deployment
metadata:
  name: high-insights-service-deployment
  labels:
    app: high-insights-service
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: high-insights-service
  template:
    metadata:
      labels:
        app: high-insights-service
        version: v1
    spec:
      containers:
      - name: high-insights-service
        image: gcr.io/highcapital-470117/high-insights-service:${IMAGE_TAG}
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DEBUG
          value: "False"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        readinessProbe:
          httpGet:
            path: /
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
