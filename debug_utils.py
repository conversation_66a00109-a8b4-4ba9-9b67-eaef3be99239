"""
Utilitários para debugging de queries e dados
"""
from sqlalchemy.orm import Session
from sqlalchemy import text, inspect
import logging
import json

# Configurar logger específico para debugging
debug_logger = logging.getLogger('multitenancy_debug')
debug_logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
formatter = logging.Formatter('🐛 DEBUG: %(message)s')
handler.setFormatter(formatter)
debug_logger.addHandler(handler)

def debug_query_result(query, result, description="Query"):
    """
    Função para debugar o resultado de uma query
    """
    debug_logger.info(f"📊 {description}")
    debug_logger.info(f"🔍 Query: {query}")
    
    if hasattr(result, '__iter__') and not isinstance(result, (str, bytes)):
        # Lista de resultados
        result_list = list(result)
        debug_logger.info(f"📝 Resultados encontrados: {len(result_list)}")
        for i, item in enumerate(result_list[:3]):  # Mostrar apenas os primeiros 3
            debug_logger.info(f"   [{i}]: {item}")
        if len(result_list) > 3:
            debug_logger.info(f"   ... e mais {len(result_list) - 3} itens")
    else:
        # Resultado único
        debug_logger.info(f"📝 Resultado: {result}")
    
    debug_logger.info("─" * 50)

def debug_table_info(db: Session, table_name: str):
    """
    Mostra informações sobre uma tabela específica
    """
    try:
        # Contar registros
        count_query = text(f"SELECT COUNT(*) FROM {table_name}")
        count_result = db.execute(count_query).scalar()
        
        # Mostrar alguns registros
        sample_query = text(f"SELECT * FROM {table_name} LIMIT 3")
        sample_result = db.execute(sample_query).fetchall()
        
        debug_logger.info(f"📋 Tabela: {table_name}")
        debug_logger.info(f"📊 Total de registros: {count_result}")
        debug_logger.info("📝 Amostras dos dados:")
        
        for i, row in enumerate(sample_result):
            debug_logger.info(f"   [{i}]: {dict(row._mapping)}")
        
        debug_logger.info("─" * 50)
        
    except Exception as e:
        debug_logger.error(f"❌ Erro ao consultar tabela {table_name}: {e}")

def debug_session_queries(db: Session):
    """
    Mostra informações sobre a sessão atual do banco
    """
    debug_logger.info("🔗 Informações da sessão do banco:")
    debug_logger.info(f"   Engine: {db.get_bind()}")
    debug_logger.info(f"   URL: {db.get_bind().url}")
    debug_logger.info("─" * 50)

def debug_model_data(model_instance, model_name="Model"):
    """
    Debug dos dados de uma instância de modelo
    """
    if model_instance is None:
        debug_logger.info(f"❌ {model_name}: None (não encontrado)")
        return
    
    debug_logger.info(f"✅ {model_name} encontrado:")
    
    # Obter todos os atributos do modelo
    inspector = inspect(model_instance.__class__)
    for column in inspector.columns:
        value = getattr(model_instance, column.name, 'N/A')
        debug_logger.info(f"   {column.name}: {value}")
    
    debug_logger.info("─" * 50)